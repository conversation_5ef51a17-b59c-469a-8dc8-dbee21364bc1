import { Button, ButtonGroup, type ChakraProps, Text } from '@chakra-ui/react';
import {
  createContext,
  type ForwardedRef,
  type RefObject,
  useContext,
  useRef,
} from 'react';
import {
  type Control,
  type FieldValues,
  useController,
  type UseControllerProps,
} from 'react-hook-form';

import { ColorSchemes } from '../../chakra-theme/foundations/colors';

type ToggleProps<T> = {
  name: string;
  value?: T;
  inputRef?: RefObject<HTMLInputElement>;
  onChange: (val: T) => void;
  children?: Array<React.ReactElement<ToggleItemProps>>;
  isDisabled?: boolean;
} & ChakraProps;

const ToggleContext = createContext<
  | {
      name: string;
      value?: unknown;
      inputRef?: ForwardedRef<HTMLInputElement>;
      isDisabled?: boolean;
      onChange: (val: unknown) => void;
    }
  | undefined
>(undefined);

export const Toggle = <T,>({
  isDisabled,
  value,
  onChange,
  inputRef,
  children,
  name,
  ...chakraProps
}: ToggleProps<T>) => {
  return (
    <ButtonGroup {...chakraProps} isAttached role="radiogroup">
      <ToggleContext.Provider
        value={{ isDisabled, value, name, inputRef, onChange }}
      >
        {children}
      </ToggleContext.Provider>
    </ButtonGroup>
  );
};
type ToggleItemProps = {
  value: string | number;
  children?: React.ReactNode;
  dataCy?: string;
  colorScheme?: ColorSchemes;
};

const getColors = (
  c = ColorSchemes.PRIMARY,
): { bg: string; borderColor: string } => {
  switch (c) {
    case ColorSchemes.NEUTRAL:
      return {
        bg: `${c}.100`,
        borderColor: `${c}.800`,
      };
    default:
      return {
        bg: `${c}.100`,
        borderColor: `${c}.600`,
      };
  }
};

export const ToggleItem = ({
  value,
  children,
  dataCy,
  colorScheme,
}: ToggleItemProps) => {
  const ctx = useContext(ToggleContext);
  const isSelected = value === ctx?.value;

  const colors = getColors(colorScheme);

  return (
    <Button
      _active={{ bg: 'neutral.50' }}
      _disabled={{
        bg: 'white',
        cursor: 'not-allowed',
        borderColor: 'neutral.200',
        color: 'neutral.500',
      }}
      _focus={{ bg: 'neutral.50' }}
      _hover={{ bg: 'neutral.50' }}
      _selected={{
        ...colors,
        borderWidth: '2px',
        px: 0,
        _disabled: {
          ...colors,
          color: 'neutral.must',
        },
      }}
      aria-selected={isSelected}
      as="label"
      bg="white"
      border="1px solid"
      borderColor="neutral.200"
      borderRadius="0.25rem"
      color="neutral.must"
      css={{
        '&:first-of-type:not(:last-of-type):not([aria-selected=true])': {
          borderRight: 'none',
          paddingRight: '2px',
        },
        '&:not(:first-of-type):last-of-type:not([aria-selected=true])': {
          borderLeft: 'none',
          paddingLeft: '2px',
        },
        '&:not(:first-of-type):not(:last-of-type):not([aria-selected=true])': {
          borderRight: 'none',
          borderLeft: 'none',
          paddingRight: '2px',
          paddingLeft: '2px',
        },
      }}
      cursor="pointer"
      data-cy={dataCy}
      flexGrow={1}
      height="2.5rem"
      isDisabled={ctx?.isDisabled}
      onClick={() => !ctx?.isDisabled && !isSelected && ctx?.onChange(value)}
      position="relative"
      px="1px"
      transition="all 0.25s, border-width 0s, border-color 0s, padding 0s"
    >
      <Text textStyle="body2-highlight">{children}</Text>
      <input
        disabled={ctx?.isDisabled}
        name={ctx?.name}
        ref={ctx?.inputRef}
        style={{
          position: 'absolute',
          left: 0,
          top: 0,
          opacity: 0,
          width: 0,
          height: 0,
        }}
        type="radio"
        value={value}
      />
    </Button>
  );
};
type ControlledToggleProps<T extends FieldValues> = {
  defaultValue?: unknown;
  control: Control<T, object>;
} & Omit<ToggleProps<T>, 'value' | 'onChange'>;

export const ControlledToggle = <T extends FieldValues>({
  name,
  rules,
  defaultValue,
  control,
  ...toggleProps
}: ControlledToggleProps<T> & UseControllerProps<T>) => {
  const ref = useRef<HTMLInputElement>(null);
  const { field } = useController({ name, rules, defaultValue, control });

  return (
    <Toggle<T>
      {...toggleProps}
      inputRef={ref}
      name={name}
      onChange={(val: T) => {
        if (field.value !== val) {
          field.onChange(val);
        }
      }}
      value={field.value}
    />
  );
};
