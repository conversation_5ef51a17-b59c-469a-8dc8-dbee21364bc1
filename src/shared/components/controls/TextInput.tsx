import {
  FormErrorIcon,
  FormErrorMessage,
  Input,
  InputGroup,
  InputLeftElement,
  InputRightElement,
  useMultiStyleConfig,
} from '@chakra-ui/react';
import { type ChangeEventHandler, forwardRef } from 'react';
import { FiAlertCircle } from 'react-icons/fi';

import { type BaseInputProps, InputWrapper } from './common';

type Styles = Record<string, string | number>;

type AdditionalStyles = Record<string, string | number | Styles>;

export type TextInputProps = {
  autoComplete?: string;
  autoFocus?: boolean;
  type?: string;
  value?: string;
  startElement?: JSX.Element;
  endElement?: JSX.Element;
  inputMode?: React.HTMLAttributes<HTMLInputElement>['inputMode'];
  placeholder?: string;
  variant?: string;
  additionalStyles?: AdditionalStyles;
  defaultValue?: string;
  readOnly?: boolean;
  subText?: string;

  onChange?: ChangeEventHandler<HTMLInputElement>;
} & BaseInputProps;

export const TextInput = forwardRef<HTMLInputElement, TextInputProps>(
  (
    {
      type = 'text',
      label,
      subText,
      placeholder,
      name,
      error,
      hint,
      autoComplete,
      value,
      isDisabled,
      startElement,
      endElement,
      onChange,
      inputMode,
      autoFocus,
      readOnly,
      variant,
      additionalStyles,
      defaultValue,

      ...chackraProps
    },
    ref,
  ) => {
    const styles = useMultiStyleConfig('Input', {});
    return (
      <InputWrapper
        error={error}
        hint={hint}
        isDisabled={isDisabled}
        label={label}
        name={name}
        subText={subText}
        {...chackraProps}
      >
        <InputGroup>
          {!!startElement && (
            <InputLeftElement width={12}>{startElement}</InputLeftElement>
          )}
          <Input
            _focus={{
              ...(styles.field as Record<string, unknown>)?._focus,
              pl: startElement ? 'calc(3rem - 1px)' : undefined,
              pr: endElement || error ? 'calc(3rem - 1px)' : undefined,
            }}
            autoComplete={autoComplete}
            autoFocus={autoFocus}
            defaultValue={defaultValue}
            inputMode={inputMode}
            name={name}
            onChange={onChange}
            pl={startElement ? 12 : undefined}
            placeholder={placeholder}
            pr={endElement || error ? 12 : undefined}
            readOnly={readOnly}
            ref={ref}
            sx={additionalStyles}
            type={type}
            value={value}
            variant={variant}
          />

          {!!(endElement || error) && (
            <InputRightElement width={12}>
              {endElement}
              {!endElement && (
                <FormErrorMessage>
                  <FormErrorIcon
                    as={FiAlertCircle}
                    boxSize={6}
                    color="red.700"
                  />
                </FormErrorMessage>
              )}
            </InputRightElement>
          )}
        </InputGroup>
      </InputWrapper>
    );
  },
);
TextInput.displayName = 'TextInput';
